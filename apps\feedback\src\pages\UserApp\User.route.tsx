import { USER_ROUTES } from "@/app.routes";
import UserLayout from "./User.layout";
import Dashboard from "./dashboard/Dashboard";
import SurveyList from "./surveyList/SurveyList";
import Terms from "./terms/Terms";

const UserRoute = [
  {
    path: USER_ROUTES().default,
    element: <UserLayout />,
    children: [
      {
        path: USER_ROUTES().dashboard.default,
        element: <Dashboard />,
        children: [
          {
            path: USER_ROUTES().dashboard.surveyList,
            element: <SurveyList />,
          },
          {
            path: "/user/dashboard/terms/:id",
            element: <Terms />,
          }
        ],
      }
    ],
  },
];

export default UserRoute;
