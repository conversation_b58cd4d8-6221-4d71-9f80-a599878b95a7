import { USER_ROUTES } from "@/app.routes";
import UserLayout from "./User.layout";
import Dashboard from "./dashboard/Dashboard";
import SurveyList from "./surveyList/SurveyList";
import Terms from "./terms/Terms";
import UpwardReview from "./upwardReview/UpwardReview";
import EngagementSurvey from "./engagementSurvey/EngagementSurvey";
import SelfEvaluation from "./selfEvaluation/SelfEvaluation";
import ExitSurvey from "./exitSurvey/ExitSurvey";

const UserRoute = [
  {
    path: USER_ROUTES().default,
    element: <UserLayout />,
    children: [
      {
        path: USER_ROUTES().dashboard.default,
        element: <Dashboard />,
        children: [
          {
            path: USER_ROUTES().dashboard.surveyList,
            element: <SurveyList />,
          },
          {
            path: "/user/dashboard/terms/:id",
            element: <Terms />,
          },
          {
            path: "/user/dashboard/survey/:id/upward/*",
            element: <UpwardReview />,
          },
          {
            path: "/user/dashboard/survey/:id/upward/pairings",
            element: <UpwardReview />,
          },
          {
            path: "/user/dashboard/survey/:surveyId/engagment",
            element: <EngagementSurvey />,
          },
          {
            path: "/user/dashboard/survey/:id/self-evaluation",
            element: <SelfEvaluation />,
          },
          {
            path: "/user/dashboard/survey/:id/exit",
            element: <ExitSurvey />,
          }
        ],
      }
    ],
  },
];

export default UserRoute;
