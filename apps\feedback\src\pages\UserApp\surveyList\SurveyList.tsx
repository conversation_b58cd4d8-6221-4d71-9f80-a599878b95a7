import { useState, useEffect } from 'react';
import { useSurveys, useSurveyStatus } from '../../../hooks/useSurveys';
import { SurveysSection } from '../../../components/survey/SurveysSection';

function SurveyList() {
  const [page, setPage] = useState(1);
  const pageSize = 10;
  
  const {
    surveys,
    statsMap,
    loading,
    error,
    totalCount,
    refetch
  } = useSurveys(page, pageSize);
  
  const { isOngoing } = useSurveyStatus();
  
  const handleViewDetails = (surveyId: string) => {
    // Handle view details action
    console.log('View details for survey:', surveyId);
  };
  
  const ongoingSurveys = surveys.filter(isOngoing);
  const upcomingSurveys = surveys.filter(survey => !isOngoing(survey));

  if (error) {
    return (
      <div className="flex min-h-screen w-full items-center justify-center text-red-500">
        {error}
      </div>
    );
  }


  return (
    <div className="w-full p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold">Surveys</h1>
      </div>
      
      <SurveysSection
        title="On-Going"
        surveys={ongoingSurveys}
        statsMap={statsMap}
        loading={loading && ongoingSurveys.length === 0}
        isOngoing={isOngoing}
        onViewDetails={handleViewDetails}
        emptyMessage="No ongoing surveys available."
      />
      
      <SurveysSection
        title="Upcoming"
        surveys={upcomingSurveys}
        statsMap={statsMap}
        loading={loading && upcomingSurveys.length === 0}
        isOngoing={isOngoing}
        onViewDetails={handleViewDetails}
        emptyMessage="No upcoming surveys available."
      />
    </div>
  );
}

export default SurveyList;
