import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Button } from '@repo/ui/components/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@repo/ui/components/card';
import { Progress } from '@repo/ui/components/progress';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Badge } from '@repo/ui/components/badge';
import { USER_ROUTES } from '@/app.routes';
import axiosInstance from '@/lib/axios';

interface SurveyMeta {
  title: string;
  endDate: string;
  canSubmit: boolean;
  nextTitle: string;
  buttonType: string;
  hideBack: boolean;
}

interface Section {
  id: number;
  title: string;
  completed: boolean;
}

interface Question {
  id: number;
  title: string;
  type: string;
  feedback?: string;
  hasFeedback: boolean;
}

const EngagementSurvey: React.FC = () => {
  const { surveyId } = useParams<{ surveyId: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [meta, setMeta] = useState<SurveyMeta | null>(null);
  const [sections, setSections] = useState<Section[]>([]);
  const [currentSection, setCurrentSection] = useState<Section | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [responseId, setResponseId] = useState<string>('');
  const [completion, setCompletion] = useState(0);
  const [confirmSubmit, setConfirmSubmit] = useState(false);

  useEffect(() => {
    if (surveyId) {
      fetchSurveyData();
    }
  }, [surveyId]);

  const fetchSurveyData = async () => {
    try {
      setIsLoading(true);
      // This would be the actual API call to get engagement survey data
      const response = await axiosInstance.get(`/survey/engagement/${surveyId}/responses/`);
      
      // Mock data structure based on legacy app
      const mockData = {
        meta: {
          title: 'Engagement Survey',
          endDate: '2024-12-31',
          canSubmit: false,
          nextTitle: 'Next',
          buttonType: 'primary',
          hideBack: false
        },
        sections: [
          { id: 1, title: 'General Questions', completed: false },
          { id: 2, title: 'Work Environment', completed: false },
          { id: 3, title: 'Career Development', completed: false }
        ],
        questions: [
          { id: 1, title: 'How satisfied are you with your current role?', type: 'radio', hasFeedback: true },
          { id: 2, title: 'Rate your work-life balance', type: 'rating', hasFeedback: false }
        ],
        responseId: 'resp_123',
        completion: 25
      };

      setMeta(mockData.meta);
      setSections(mockData.sections);
      setCurrentSection(mockData.sections[0]);
      setQuestions(mockData.questions);
      setResponseId(mockData.responseId);
      setCompletion(mockData.completion);
      setError(null);
    } catch (err) {
      console.error('Error fetching survey data:', err);
      setError('Failed to load survey data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNext = () => {
    if (!currentSection || !sections) return;

    const currentIndex = sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex < sections.length - 1) {
      setCurrentSection(sections[currentIndex + 1]);
    } else if (meta?.canSubmit) {
      setConfirmSubmit(true);
    }
  };

  const handleBack = () => {
    if (!currentSection || !sections) return;

    const currentIndex = sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex > 0) {
      setCurrentSection(sections[currentIndex - 1]);
    }
  };

  const handleSubmit = async () => {
    try {
      setIsSaving(true);
      // Submit survey logic here
      await axiosInstance.post(`/survey/engagement/${surveyId}/submit/`);
      navigate(USER_ROUTES().dashboard.surveyList);
    } catch (err) {
      console.error('Error submitting survey:', err);
      setError('Failed to submit survey');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSectionSelect = (section: Section) => {
    setCurrentSection(section);
    setConfirmSubmit(false);
  };

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (confirmSubmit) {
    return (
      <div className="min-h-screen flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Submit Survey</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p>Are you ready to submit your responses?</p>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => setConfirmSubmit(false)}
                className="flex-1"
              >
                Back
              </Button>
              <Button 
                onClick={handleSubmit}
                disabled={isSaving}
                className="flex-1"
              >
                {isSaving ? 'Submitting...' : 'Submit'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <Button 
                variant="ghost" 
                onClick={() => navigate(USER_ROUTES().dashboard.surveyList)}
                className="mb-2"
              >
                ← Back to Surveys
              </Button>
              <h1 className="text-2xl font-bold">
                {isLoading ? <Skeleton className="h-8 w-64" /> : meta?.title}
              </h1>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Progress</p>
              <p className="font-semibold">{completion}% Complete</p>
            </div>
          </div>
          <Progress value={completion} className="mt-4" />
        </div>
      </div>

      <div className="max-w-7xl mx-auto flex">
        {/* Sidebar */}
        <div className="w-80 bg-white border-r min-h-screen p-6">
          <div className="space-y-6">
            {/* Survey Info */}
            <div>
              <h3 className="font-semibold mb-2">Survey Ends On</h3>
              <p className="text-sm text-gray-600">
                {isLoading ? <Skeleton className="h-4 w-32" /> : meta?.endDate}
              </p>
            </div>

            {/* Progress */}
            <div>
              <h3 className="font-semibold mb-2">Survey Status</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Questions</span>
                  <span>{questions.length}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Completed</span>
                  <span>{Math.round((completion / 100) * questions.length)}</span>
                </div>
              </div>
            </div>

            {/* Sections */}
            <div>
              <h3 className="font-semibold mb-2">Categories</h3>
              <div className="space-y-1">
                {isLoading ? (
                  Array(3).fill(0).map((_, index) => (
                    <Skeleton key={index} className="h-8 w-full" />
                  ))
                ) : (
                  sections.map((section) => (
                    <Button
                      key={section.id}
                      variant={currentSection?.id === section.id ? 'default' : 'ghost'}
                      className="w-full justify-start"
                      onClick={() => handleSectionSelect(section)}
                    >
                      <span className="flex-1 text-left">{section.title}</span>
                      {section.completed && (
                        <Badge variant="secondary" className="ml-2">✓</Badge>
                      )}
                    </Button>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          {isLoading ? (
            <div className="space-y-6">
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-32 w-full" />
            </div>
          ) : (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">{currentSection?.title}</h2>
              
              {/* Questions would be rendered here */}
              <Card>
                <CardContent className="p-6">
                  <p className="text-gray-600">
                    Survey questions will be implemented here using the survey taking components.
                  </p>
                </CardContent>
              </Card>

              {/* Navigation */}
              <div className="flex justify-between">
                <Button 
                  variant="outline" 
                  onClick={handleBack}
                  disabled={meta?.hideBack}
                >
                  Back
                </Button>
                <Button onClick={handleNext}>
                  {meta?.nextTitle || 'Next'}
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EngagementSurvey;
