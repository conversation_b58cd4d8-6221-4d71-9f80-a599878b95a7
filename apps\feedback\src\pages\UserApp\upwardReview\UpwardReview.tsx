import React from 'react';
import { useParams, Navigate } from 'react-router';
import { USER_ROUTES } from '@/app.routes';
import Pairings from './Pairings';

interface ParamTypes {
  id?: string;
}

export default function UpwardReview() {
  const { id } = useParams<ParamTypes>();

  // Default redirect to pairings page (similar to legacy app)
  if (!window.location.pathname.includes('/pairings') && !window.location.pathname.includes('/take')) {
    return <Navigate to={USER_ROUTES().dashboard.upwardReview.getPairingsUrl(id || "")} replace />;
  }

  // If we're on the pairings route, show the Pairings component
  if (window.location.pathname.includes('/pairings')) {
    return <Pairings />;
  }

  // If we're on the take survey route, we'll handle that later
  if (window.location.pathname.includes('/take')) {
    return <div>Take Survey Component - To be implemented</div>;
  }

  return <Pairings />;
}
