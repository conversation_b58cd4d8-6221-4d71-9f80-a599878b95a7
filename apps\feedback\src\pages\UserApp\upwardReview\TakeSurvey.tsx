import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router';
import { Button } from '@repo/ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@repo/ui/components/card';
import { Progress } from '@repo/ui/components/progress';
import { Badge } from '@repo/ui/components/badge';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@repo/ui/components/dialog';
import { ScrollArea } from '@repo/ui/components/scroll-area';
import { Separator } from '@repo/ui/components/separator';
import { ArrowLeft, Menu, HelpCircle } from 'lucide-react';
import { USER_ROUTES } from '@/app.routes';
import axiosInstance from '@/lib/axios';
import Question<PERSON>enderer from './components/QuestionRenderer';
import SubmitConfirmation from './components/SubmitConfirmation';

interface SurveyMeta {
  title: string;
  surveyFor: string;
  endDate: string;
  lastModified: string;
  canSubmit: boolean;
  nextTitle: string;
  buttonType: string;
  hideBack: boolean;
  indexId: string;
}

interface Section {
  id: number;
  title: string;
  completed: boolean;
}

interface Question {
  id: number;
  title: string;
  type: string;
  feedback?: string;
  hasFeedback: boolean;
  options?: any[];
  response?: any;
}

interface Analytics {
  total: number;
  completed: number;
  sections: number[];
}

const TakeSurvey: React.FC = () => {
  const { id, surveyId } = useParams<{ id: string; surveyId: string }>();
  const navigate = useNavigate();
  
  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [meta, setMeta] = useState<SurveyMeta | null>(null);
  const [sections, setSections] = useState<Section[]>([]);
  const [currentSection, setCurrentSection] = useState<Section | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [analytics, setAnalytics] = useState<Analytics>({ total: 0, completed: 0, sections: [] });
  const [error, setError] = useState<string | null>(null);
  const [confirmSubmit, setConfirmSubmit] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false);
  const [faqs, setFaqs] = useState<any[]>([]);
  const [showFaqDialog, setShowFaqDialog] = useState(false);

  useEffect(() => {
    if (id && surveyId) {
      fetchSurveyData();
    }
  }, [id, surveyId]);

  useEffect(() => {
    if (meta?.indexId) {
      fetchFAQs();
    }
  }, [meta?.indexId]);

  const fetchSurveyData = async () => {
    try {
      setIsLoading(true);
      // Fetch survey response data
      const response = await axiosInstance.get(`/survey/response/${id}/`);
      const data = response.data;

      // Mock data structure based on legacy app - replace with actual API response
      const mockData = {
        meta: {
          title: data.survey?.title || 'Survey',
          surveyFor: data.target?.first_name + ' ' + data.target?.last_name || 'Unknown',
          endDate: data.survey?.deadline || '2024-12-31',
          lastModified: data.updated_at || 'Just now',
          canSubmit: false,
          nextTitle: 'Next',
          buttonType: 'primary',
          hideBack: false,
          indexId: data.survey?.index || ''
        },
        sections: [
          { id: 1, title: 'Leadership Skills', completed: false },
          { id: 2, title: 'Communication', completed: false },
          { id: 3, title: 'Team Collaboration', completed: false },
          { id: 4, title: 'Problem Solving', completed: false }
        ],
        questions: [
          {
            id: 1,
            title: 'How would you rate their leadership abilities?',
            type: 'radio',
            hasFeedback: true,
            options: [
              { id: 1, text: 'Excellent', value: 5 },
              { id: 2, text: 'Good', value: 4 },
              { id: 3, text: 'Average', value: 3 },
              { id: 4, text: 'Below Average', value: 2 },
              { id: 5, text: 'Poor', value: 1 }
            ]
          },
          {
            id: 2,
            title: 'Please provide specific examples of their leadership in action.',
            type: 'textarea',
            hasFeedback: false
          }
        ],
        analytics: {
          total: 20,
          completed: 5,
          sections: [1]
        }
      };

      setMeta(mockData.meta);
      setSections(mockData.sections);
      setCurrentSection(mockData.sections[0]);
      setQuestions(mockData.questions);
      setAnalytics(mockData.analytics);
      setError(null);
    } catch (err) {
      console.error('Error fetching survey data:', err);
      setError('Failed to load survey data');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchFAQs = async () => {
    try {
      const response = await axiosInstance.get(`/survey/faq/`, {
        params: { survey_index: meta?.indexId }
      });
      setFaqs(response.data || []);
    } catch (err) {
      console.warn('Failed to fetch FAQs:', err);
    }
  };

  const handleSectionSelect = (section: Section) => {
    setCurrentSection(section);
    setConfirmSubmit(false);
    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleNext = () => {
    if (!currentSection || !sections) return;

    const currentIndex = sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex < sections.length - 1) {
      setCurrentSection(sections[currentIndex + 1]);
    } else if (meta?.canSubmit) {
      setConfirmSubmit(true);
    }
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleBack = () => {
    if (!currentSection || !sections) return;

    const currentIndex = sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex > 0) {
      setCurrentSection(sections[currentIndex - 1]);
    }
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleSubmit = async () => {
    try {
      setIsSaving(true);
      await axiosInstance.post(`/survey/response/${id}/submit/`);
      // Navigate back to pairings
      navigate(USER_ROUTES().dashboard.upwardReview.getPairingsUrl(meta?.indexId || ''));
    } catch (err) {
      console.error('Error submitting survey:', err);
      setError('Failed to submit survey');
    } finally {
      setIsSaving(false);
    }
  };

  const handleQuestionUpdate = async (questionId: number, response: any, feedback?: string) => {
    try {
      setIsSaving(true);
      // Update question response
      await axiosInstance.patch(`/survey/response/${id}/question/${questionId}/`, {
        response,
        feedback
      });
      
      // Update local state
      setQuestions(prev => prev.map(q => 
        q.id === questionId ? { ...q, response, feedback } : q
      ));
    } catch (err) {
      console.error('Error updating question:', err);
    } finally {
      setIsSaving(false);
    }
  };

  const getCompletionPercentage = () => {
    if (analytics.total === 0) return 0;
    return Math.round((analytics.completed / analytics.total) * 100);
  };

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (confirmSubmit) {
    return (
      <SubmitConfirmation
        meta={meta}
        analytics={analytics}
        onBack={() => setConfirmSubmit(false)}
        onSubmit={handleSubmit}
        isLoading={isSaving}
      />
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile Menu Button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          variant="outline"
          size="icon"
          onClick={() => setShowSidebar(true)}
          className="bg-background/80 backdrop-blur-sm"
        >
          <Menu className="h-4 w-4" />
        </Button>
      </div>

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-40 w-80 bg-background border-r transform transition-transform duration-300 ease-in-out
        ${showSidebar ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 lg:static lg:inset-0
      `}>
        <ScrollArea className="h-full">
          <div className="p-6 space-y-6">
            {/* Back Button */}
            <Button
              variant="ghost"
              onClick={() => navigate(USER_ROUTES().dashboard.upwardReview.getPairingsUrl(meta?.indexId || ''))}
              className="w-full justify-start"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Pairings
            </Button>

            <Separator />

            {/* Survey Info */}
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-sm text-muted-foreground">Survey Ends On</h3>
                <p className="font-medium">
                  {isLoading ? <Skeleton className="h-4 w-32" /> : meta?.endDate}
                </p>
              </div>

              {faqs.length > 0 && (
                <Dialog open={showFaqDialog} onOpenChange={setShowFaqDialog}>
                  <DialogTrigger asChild>
                    <Button variant="outline" className="w-full">
                      <HelpCircle className="mr-2 h-4 w-4" />
                      FAQs
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl max-h-[80vh]">
                    <DialogHeader>
                      <DialogTitle>Frequently Asked Questions</DialogTitle>
                    </DialogHeader>
                    <ScrollArea className="max-h-[60vh]">
                      <div className="space-y-4">
                        {faqs.map((faq, index) => (
                          <div key={index} className="space-y-2">
                            <h4 className="font-medium">{faq.question}</h4>
                            <p className="text-sm text-muted-foreground">{faq.answer}</p>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </DialogContent>
                </Dialog>
              )}
            </div>

            <Separator />

            {/* Feedback For */}
            <div className="space-y-2">
              <h3 className="font-semibold text-sm text-muted-foreground">Feedback For</h3>
              <h2 className="font-semibold text-lg">
                {isLoading ? <Skeleton className="h-6 w-40" /> : meta?.surveyFor}
              </h2>
            </div>

            {/* Progress */}
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Questions</span>
                <span>{analytics.total}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Completed</span>
                <span className="text-green-600">{analytics.completed}</span>
              </div>
              <Progress value={getCompletionPercentage()} className="h-2" />
            </div>

            <Separator />

            {/* Categories */}
            <div className="space-y-3">
              <h3 className="font-semibold text-sm text-muted-foreground">Categories</h3>
              <div className="space-y-1">
                {isLoading ? (
                  Array(4).fill(0).map((_, index) => (
                    <Skeleton key={index} className="h-10 w-full" />
                  ))
                ) : (
                  sections.map((section) => (
                    <Button
                      key={section.id}
                      variant={currentSection?.id === section.id ? 'default' : 'ghost'}
                      className="w-full justify-between"
                      onClick={() => handleSectionSelect(section)}
                    >
                      <span>{section.title}</span>
                      {analytics.sections.includes(section.id) && (
                        <Badge variant="secondary" className="ml-2">✓</Badge>
                      )}
                    </Button>
                  ))
                )}
              </div>
            </div>
          </div>
        </ScrollArea>
      </div>

      {/* Overlay for mobile */}
      {showSidebar && (
        <div
          className="fixed inset-0 bg-black/50 z-30 lg:hidden"
          onClick={() => setShowSidebar(false)}
        />
      )}

      {/* Main Content */}
      <div className="lg:ml-80">
        {/* Header */}
        <div className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b sticky top-0 z-20">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold">
                  {isLoading ? <Skeleton className="h-8 w-64" /> : meta?.title}
                </h1>
                <p className="text-sm text-muted-foreground mt-1">
                  {isLoading ? <Skeleton className="h-4 w-48" /> : currentSection?.title}
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm text-muted-foreground">Progress</p>
                <p className="font-semibold">{getCompletionPercentage()}% Complete</p>
              </div>
            </div>
            <Progress value={getCompletionPercentage()} className="mt-4" />
          </div>
        </div>

        {/* Questions */}
        <div className="p-6 space-y-6">
          {isLoading ? (
            Array(3).fill(0).map((_, index) => (
              <Card key={index}>
                <CardHeader>
                  <Skeleton className="h-6 w-3/4" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-32 w-full" />
                </CardContent>
              </Card>
            ))
          ) : (
            questions.map((question, index) => (
              <QuestionRenderer
                key={question.id}
                question={question}
                questionNumber={index + 1}
                onUpdate={(response, feedback) => handleQuestionUpdate(question.id, response, feedback)}
                isSaving={isSaving}
              />
            ))
          )}
        </div>

        {/* Footer Navigation */}
        {!isLoading && (
          <div className="border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky bottom-0">
            <div className="px-6 py-4 flex justify-between">
              <Button
                variant="outline"
                onClick={handleBack}
                disabled={meta?.hideBack || sections.findIndex(s => s.id === currentSection?.id) === 0}
              >
                Back
              </Button>
              <Button
                onClick={handleNext}
                disabled={isSaving}
              >
                {meta?.nextTitle || 'Next'}
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TakeSurvey;
